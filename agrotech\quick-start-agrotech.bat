@echo off
REM ============================================================================
REM AgroTech Website - Quick Start Launcher
REM ============================================================================
REM This is a simplified launcher that starts the server and opens the browser
REM ============================================================================

title AgroTech Quick Launcher
color 0B

echo.
echo ============================================================================
echo                    AgroTech Website - Quick Launcher
echo ============================================================================
echo.

REM Quick Python check
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found! Please install Python first.
    pause
    exit /b 1
)

REM Navigate to agrotech directory
cd /d "%~dp0agrotech"
if %errorlevel% neq 0 (
    echo [ERROR] Cannot find agrotech directory
    pause
    exit /b 1
)

echo [INFO] Starting server and opening browser...
echo.
echo Access URLs:
echo   Homepage: http://localhost:8000/index.html
echo   Products: http://localhost:8000/products.html
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start server in background and open browser
start "" "http://localhost:8000/index.html"
python -m http.server 8000

echo.
echo [INFO] Server stopped. Press any key to exit.
pause >nul
