# AgroTech Website - Local Development Server

This directory contains batch files to easily start the local development server for the AgroTech agricultural equipment website.

## 📁 Files Included

### `start-agrotech-server.bat`
**Full-featured server launcher with comprehensive error checking**
- Checks Python installation and version
- Verifies directory structure and essential files
- Provides detailed server information and access URLs
- Displays helpful error messages and troubleshooting tips
- Shows server logs in real-time
- Recommended for development and debugging

### `quick-start-agrotech.bat`
**Simple launcher that starts server and opens browser**
- Quick Python check
- Automatically opens homepage in default browser
- Minimal output for clean experience
- Perfect for quick testing and demonstrations

## 🚀 How to Use

### Method 1: Double-Click (Easiest)
1. Double-click either `start-agrotech-server.bat` or `quick-start-agrotech.bat`
2. The server will start automatically
3. Access the website at the URLs shown in the console

### Method 2: Command Prompt
1. Open Command Prompt in this directory
2. Run: `start-agrotech-server.bat` or `quick-start-agrotech.bat`

## 🌐 Website Access URLs

Once the server is running, access these pages:

- **Homepage**: http://localhost:8000/index.html
- **Products**: http://localhost:8000/products.html
- **About**: http://localhost:8000/about.html
- **Contact**: http://localhost:8000/contact.html
- **Gallery**: http://localhost:8000/photo-gallery.html

## ⚙️ Requirements

- **Python 3.x** must be installed and accessible via PATH
- Download from: https://python.org/downloads/
- ⚠️ **Important**: Check "Add Python to PATH" during installation

## 🛠️ Troubleshooting

### "Python is not recognized"
- Install Python from https://python.org/downloads/
- During installation, check "Add Python to PATH"
- Restart Command Prompt after installation

### "agrotech directory not found"
- Ensure batch files are in: `c:\Users\<USER>\Documents\augment-projects\`
- Ensure `agrotech` folder exists in the same directory

### Port 8000 already in use
- Close any other applications using port 8000
- Or modify the batch file to use a different port (e.g., 8080)

### Website not loading
- Check that the server is running (console should show "Serving HTTP")
- Verify the URL is correct: http://localhost:8000/index.html
- Check firewall settings if needed

## 🔧 Customization

### Change Server Port
Edit the batch file and change `8000` to your preferred port:
```batch
python -m http.server 8080
```

### Add More URLs
Edit the batch file to include additional pages in the URL list.

## 📝 Server Logs

The server will display logs showing:
- File requests (GET requests)
- HTTP status codes (200 = success, 404 = not found)
- Client IP addresses
- Timestamps

## 🛑 Stopping the Server

- Press `Ctrl+C` in the command window
- Or simply close the command window
- The server will stop automatically

## 📞 Support

If you encounter issues:
1. Check the error messages in the console
2. Verify Python installation
3. Ensure all website files are in the agrotech directory
4. Check that no other application is using port 8000

---

**AgroTech Agricultural Equipment Website**  
Professional B2B Lead Generation Platform
