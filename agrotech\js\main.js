// Main JavaScript functionality for a.agrotech website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initMobileMenu();
    initSearch();
    initFeaturedProducts();
    initScrollAnimations();
    initFormValidation();
    initTooltips();
    initLazyLoading();
});

// Mobile Menu Toggle
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const body = document.body;

    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            mobileToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            body.classList.toggle('menu-open');
        });

        // Close menu when clicking on links
        const navLinks = navMenu.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navMenu.contains(e.target) && !mobileToggle.contains(e.target)) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
                body.classList.remove('menu-open');
            }
        });
    }
}

// Search Functionality
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (!searchInput || !searchSuggestions) return;

    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            searchSuggestions.classList.remove('active');
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetchSearchSuggestions(query);
        }, 300);
    });

    searchInput.addEventListener('focus', function() {
        if (this.value.trim().length >= 2) {
            searchSuggestions.classList.add('active');
        }
    });

    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
            searchSuggestions.classList.remove('active');
        }
    });
}

// Fetch search suggestions
function fetchSearchSuggestions(query) {
    // Mock search suggestions - in real implementation, this would be an API call
    const mockSuggestions = [
        'Rotavator',
        'Tractor',
        'Seed Drill',
        'Cultivator',
        'Harvester',
        'Sprayer',
        'Plough',
        'Thresher',
        'Disc Harrow',
        'Power Tiller'
    ];

    const filteredSuggestions = mockSuggestions.filter(item => 
        item.toLowerCase().includes(query.toLowerCase())
    );

    displaySearchSuggestions(filteredSuggestions, query);
}

// Display search suggestions
function displaySearchSuggestions(suggestions, query) {
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (suggestions.length === 0) {
        searchSuggestions.innerHTML = '<div class="suggestion-item">No suggestions found</div>';
    } else {
        searchSuggestions.innerHTML = suggestions.map(suggestion => 
            `<div class="suggestion-item" onclick="selectSuggestion('${suggestion}')">${suggestion}</div>`
        ).join('');
    }
    
    searchSuggestions.classList.add('active');
}

// Select search suggestion
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    searchInput.value = suggestion;
    searchSuggestions.classList.remove('active');
    
    // Redirect to products page with search query
    window.location.href = `products.html?search=${encodeURIComponent(suggestion)}`;
}

// Load featured products
async function initFeaturedProducts() {
    const featuredContainer = document.getElementById('featuredProducts');
    if (!featuredContainer) return;

    try {
        // Load products from JSON file
        const response = await fetch('data/products.json');
        const data = await response.json();
        const allProducts = data.products;

        // Show all 4 products as featured
        renderProducts(allProducts, featuredContainer);
    } catch (error) {
        console.error('Error loading featured products:', error);
        // Fallback to show a message
        featuredContainer.innerHTML = `
            <div class="no-products">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Featured products will be available soon</h3>
                <p>Please check back later or browse our product catalog</p>
                <a href="products.html" class="btn btn-primary">View All Products</a>
            </div>
        `;
    }
}

// Render products
function renderProducts(products, container) {
    container.innerHTML = products.map(product => `
        <div class="product-card" data-product-id="${product.id}">
            <div class="product-image">
                <div class="image-gallery">
                    <img src="${product.image}" alt="${product.name}" loading="lazy"
                         onerror="this.src='images/placeholder-product.jpg'" class="main-image">
                    ${product.images && product.images.length > 1 ? `
                        <div class="image-thumbnails">
                            ${product.images.slice(0, 4).map((img, index) => `
                                <img src="${img}" alt="${product.name} ${index + 1}"
                                     class="thumbnail ${index === 0 ? 'active' : ''}"
                                     onclick="changeMainImage('${img}', this)"
                                     onerror="this.style.display='none'">
                            `).join('')}
                            ${product.images.length > 4 ? `
                                <div class="more-images" onclick="openImageGallery(${product.id})">
                                    +${product.images.length - 4}
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
                ${product.badge ? `<span class="product-badge">${product.badge}</span>` : ''}
                ${product.certifications ? `<div class="certifications">
                    ${product.certifications.map(cert => `<span class="cert-badge">${cert}</span>`).join('')}
                </div>` : ''}
            </div>
            <div class="product-info">
                <div class="product-header">
                    <div class="product-brand">${product.brand}</div>
                    ${product.rating ? `
                        <div class="product-rating">
                            <div class="stars">
                                ${generateStars(product.rating)}
                            </div>
                            <span class="rating-text">${product.rating} (${product.reviews || 0})</span>
                        </div>
                    ` : ''}
                </div>
                <h3 class="product-title">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <ul class="product-features">
                    ${product.features.slice(0, 3).map(feature => `
                        <li><i class="fas fa-check"></i> ${feature}</li>
                    `).join('')}
                </ul>
                ${product.priceRange ? `
                    <div class="price-range">
                        <span class="price-label">Price Range:</span>
                        <span class="price-value">${product.priceRange}</span>
                    </div>
                ` : ''}
                ${product.warranty ? `
                    <div class="warranty-info">
                        <i class="fas fa-shield-alt"></i>
                        <span>${product.warranty}</span>
                    </div>
                ` : ''}
                <div class="product-actions">
                    <a href="product-detail.html?id=${product.id}" class="btn btn-primary btn-small">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <button class="btn btn-secondary btn-small" onclick="requestQuote(${product.id})">
                        <i class="fas fa-envelope"></i> Get Quote
                    </button>
                    <button class="btn btn-outline btn-small" onclick="addToComparison(${product.id})" title="Add to Compare">
                        <i class="fas fa-balance-scale"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Request quote functionality
function requestQuote(productId) {
    // Store product ID for quote form
    localStorage.setItem('quoteProductId', productId);

    // Redirect to contact page with quote parameter
    window.location.href = 'contact.html?action=quote&product=' + productId;
}

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    let starsHTML = '';

    // Full stars
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }

    // Half star
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }

    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }

    return starsHTML;
}

// Change main image in product card
function changeMainImage(imageSrc, thumbnailElement) {
    const productCard = thumbnailElement.closest('.product-card');
    const mainImage = productCard.querySelector('.main-image');
    const thumbnails = productCard.querySelectorAll('.thumbnail');

    // Update main image
    mainImage.src = imageSrc;

    // Update active thumbnail
    thumbnails.forEach(thumb => thumb.classList.remove('active'));
    thumbnailElement.classList.add('active');
}

// Open image gallery modal
function openImageGallery(productId) {
    // This would open a modal with all product images
    // For now, redirect to product detail page
    window.location.href = `product-detail.html?id=${productId}#gallery`;
}

// Add to comparison functionality
function addToComparison(productId) {
    // This would integrate with the comparison system
    if (typeof window.addToComparison === 'function') {
        const success = window.addToComparison({ id: productId });
        if (success) {
            showNotification('Product added to comparison', 'success');
        } else {
            showNotification('Maximum 3 products can be compared', 'warning');
        }
    } else {
        showNotification('Comparison feature will be available soon', 'info');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.category-card, .product-card, .testimonial-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

// Validate form
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        const errorElement = field.parentNode.querySelector('.error-message');
        
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else if (field.type === 'email' && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        } else if (field.type === 'tel' && !isValidPhone(field.value)) {
            showFieldError(field, 'Please enter a valid phone number');
            isValid = false;
        } else {
            hideFieldError(field);
        }
    });
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('error');
    
    let errorElement = field.parentNode.querySelector('.error-message');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
}

// Hide field error
function hideFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.error-message');
    if (errorElement) {
        errorElement.remove();
    }
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation
function isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}

// Initialize tooltips
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// Show tooltip
function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    setTimeout(() => tooltip.classList.add('show'), 10);
}

// Hide tooltip
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            img.classList.add('lazy');
            imageObserver.observe(img);
        });
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Show loading state
function showLoading(element) {
    element.classList.add('loading');
    const spinner = document.createElement('div');
    spinner.className = 'spinner';
    element.appendChild(spinner);
}

// Hide loading state
function hideLoading(element) {
    element.classList.remove('loading');
    const spinner = element.querySelector('.spinner');
    if (spinner) {
        spinner.remove();
    }
}
