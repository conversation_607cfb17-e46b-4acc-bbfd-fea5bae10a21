@echo off
REM ============================================================================
REM AgroTech Agricultural B2B Website - Local Development Server Starter
REM ============================================================================
REM This batch file automatically starts a Python HTTP server for the 
REM agricultural equipment website located in the agrotech directory.
REM 
REM Requirements: Python 3.x must be installed and accessible via PATH
REM Usage: Double-click this file or run from command prompt
REM ============================================================================

REM Set console title and colors
title AgroTech Website Server
color 0A

REM Display header
echo.
echo ============================================================================
echo                    AgroTech Agricultural Equipment Website
echo                           Local Development Server
echo ============================================================================
echo.

REM Check if Python is installed and accessible
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not accessible via PATH
    echo.
    echo Please install Python 3.x from https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Display Python version
for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Found %PYTHON_VERSION%
echo.

REM Get current directory and set target directory
set CURRENT_DIR=%cd%
set TARGET_DIR=%CURRENT_DIR%\agrotech

REM Check if agrotech directory exists
echo [INFO] Checking for agrotech directory...
if not exist "%TARGET_DIR%" (
    echo [ERROR] agrotech directory not found at: %TARGET_DIR%
    echo.
    echo Please ensure this batch file is in the correct location:
    echo c:\Users\<USER>\Documents\augment-projects\
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Found agrotech directory
echo.

REM Check for essential website files
echo [INFO] Verifying website files...
if not exist "%TARGET_DIR%\index.html" (
    echo [WARNING] index.html not found in agrotech directory
)
if not exist "%TARGET_DIR%\products.html" (
    echo [WARNING] products.html not found in agrotech directory
)
if not exist "%TARGET_DIR%\data\products.json" (
    echo [WARNING] products.json not found in data directory
)

REM Navigate to agrotech directory
echo [INFO] Navigating to agrotech directory...
cd /d "%TARGET_DIR%"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to navigate to agrotech directory
    pause
    exit /b 1
)

echo [SUCCESS] Changed to directory: %cd%
echo.

REM Display server information
echo ============================================================================
echo                              SERVER STARTING
echo ============================================================================
echo.
echo Server Directory: %cd%
echo Server Port:      8000
echo.
echo Access URLs:
echo   Homepage:       http://localhost:8000/index.html
echo   Products:       http://localhost:8000/products.html
echo   About:          http://localhost:8000/about.html
echo   Contact:        http://localhost:8000/contact.html
echo   Gallery:        http://localhost:8000/photo-gallery.html
echo.
echo ============================================================================
echo.
echo [INFO] Starting Python HTTP server...
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start the Python HTTP server
python -m http.server 8000

REM This section runs when the server is stopped
echo.
echo ============================================================================
echo [INFO] Server stopped
echo ============================================================================
echo.
echo Thank you for using AgroTech Website Development Server!
echo.
pause
